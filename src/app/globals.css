@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 120 50% 97%;
    --foreground: 150 30% 20%;
    --card: 0 0% 100%;
    --card-foreground: 150 30% 20%;
    --popover: 0 0% 100%;
    --popover-foreground: 150 30% 20%;
    --primary: 150 40% 45%;
    --primary-foreground: 0 0% 98%;
    --secondary: 105 50% 90%;
    --secondary-foreground: 150 30% 20%;
    --muted: 105 50% 90%;
    --muted-foreground: 150 25% 55%;
    --accent: 43 74% 66%;
    --accent-foreground: 24 9.8% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 105 40% 85%;
    --input: 105 40% 85%;
    --ring: 150 40% 45%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }
 
  .dark {
    --background: 150 15% 10%;
    --foreground: 150 10% 80%;
    --card: 150 15% 12%;
    --card-foreground: 150 10% 80%;
    --popover: 150 15% 10%;
    --popover-foreground: 150 10% 80%;
    --primary: 150 40% 55%;
    --primary-foreground: 0 0% 98%;
    --secondary: 150 15% 20%;
    --secondary-foreground: 0 0% 98%;
    --muted: 150 15% 20%;
    --muted-foreground: 150 10% 60%;
    --accent: 43 74% 56%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 150 15% 25%;
    --input: 150 15% 25%;
    --ring: 150 40% 55%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}
 
@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
